package cn.tianxing.cloud.module.system.api.sms;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.system.api.sms.dto.send.SmsSendSingleToUserReqDTO;
import cn.tianxing.cloud.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 短信发送")
public interface SmsSendApi {

    String PREFIX = ApiConstants.PREFIX + "/sms/send";

    @PostMapping(PREFIX + "/send-single-admin")
    @Operation(summary = "发送单条短信给 Admin 用户", description = "在 mobile 为空时，使用 userId 加载对应 Admin 的手机号")
    CommonResult<Long> sendSingleSmsToAdmin(@Valid @RequestBody SmsSendSingleToUserReqDTO reqDTO);

    @PostMapping(PREFIX + "/send-single-member")
    @Operation(summary = "发送单条短信给 Member 用户", description = "在 mobile 为空时，使用 userId 加载对应 Member 的手机号")
    CommonResult<Long> sendSingleSmsToMember(@Valid @RequestBody SmsSendSingleToUserReqDTO reqDTO);

    @PostMapping(PREFIX + "/send-single-sms")
    @Operation(summary = "发送单条短信给用户", description = "在 mobile 为空时，使用 userId 加载对应 Member 的手机号")
    CommonResult<Boolean> sendSingleSms(@Valid @RequestBody SmsSendSingleToUserReqDTO reqDTO);

    @PostMapping(path = PREFIX + "/mms", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "发送单条彩信给用户", description = "在 mobile 为空时，使用 userId 加载对应 Member 的手机号")
    CommonResult<Boolean> sendMms(@RequestBody SmsSendSingleToUserReqDTO reqDTO);
}
